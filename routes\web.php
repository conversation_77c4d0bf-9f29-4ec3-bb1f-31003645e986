<?php

use App\Http\Controllers\Apps\UserProfileController;
use App\Http\Controllers\Apps\PermissionManagementController;
use App\Http\Controllers\Apps\RoleManagementController;
use App\Http\Controllers\Apps\UserManagementController;
use App\Http\Controllers\Auth\SocialiteController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Frontend\HomeController;
use App\Http\Controllers\Frontend\PageController;
use App\Http\Controllers\Frontend\ProjectController;
use App\Http\Controllers\Frontend\ScholarshipController;
use App\Http\Controllers\Frontend\BloodController;
use App\Http\Controllers\Frontend\DonationController;
use App\Http\Controllers\Frontend\MediaController;
use App\Http\Controllers\Frontend\MemberController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Backend routes (existing)
Route::middleware(['auth', 'verified'])->group(function () {
    // Admin dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // User management routes
    Route::name('user-management.')->group(function () {
        Route::resource('/user-management/users', UserManagementController::class);
        Route::resource('/user-management/roles', RoleManagementController::class);
        Route::resource('/user-management/permissions', PermissionManagementController::class);
    });

    // User profile routes
    Route::name('user-profile.')->group(function () {
        Route::resource('/user-profile/overview', UserProfileController::class);
    });
});


// Frontend routes
Route::get('/', [HomeController::class, 'index'])->name('frontend.home');
Route::get('/home', [HomeController::class, 'index'])->name('frontend.home');

// Public Pages
Route::controller(PageController::class)->group(function () {
    Route::get('/about-us', 'aboutUs')->name('frontend.about-us');
    Route::get('/contact-us', 'contactUs')->name('frontend.contact-us');
    Route::get('/faq', 'faq')->name('frontend.faq');
    Route::get('/rules', 'rules')->name('frontend.rules');
});

// Member Pages
Route::controller(MemberController::class)->group(function () {
    Route::get('/committee', 'committee')->name('frontend.committee');
    Route::get('/members', 'members')->name('frontend.members');
});

// Projects Pages
Route::controller(ProjectController::class)->group(function () {
    Route::get('/projects', 'index')->name('frontend.projects');
    Route::get('/project/library-project', 'library')->name('frontend.library-project');
    Route::get('/project/tree-plantation-project', 'treePlantation')->name('frontend.tree-plantation-project');
    Route::get('/project/education-project', 'education')->name('frontend.education-project');
    Route::get('/project/food-project', 'food')->name('frontend.food-project');
    Route::get('/project/cloth-project', 'cloth')->name('frontend.cloth-project');
    Route::get('/project/talent-student-award', 'talentStudentAward')->name('frontend.talent-student-award');
});

// Scholarship Application Pages
Route::controller(ScholarshipController::class)->group(function () {
    Route::get('/scholarship', 'index')->name('frontend.scholarship');
    Route::get('/scholarship/application', 'application')->name('frontend.scholarship-application');
    Route::get('/scholarship/meritorious-student', 'meritoriousStudent')->name('frontend.meritorious-student');
});

// Blood Donor Pages
Route::controller(BloodController::class)->group(function () {
    Route::get('/blood-donor', 'index')->name('frontend.blood-donor');
    Route::get('/blood-donor/join', 'join')->name('frontend.blood-donor-join');
});

// Donation Pages
Route::controller(DonationController::class)->group(function () {
    Route::get('/donation', 'index')->name('frontend.donation');
    Route::get('/about-donation', 'about')->name('frontend.about-donation');
    Route::get('/sponsor-info', 'sponsor')->name('frontend.sponsor-info');
});

// Media Pages
Route::controller(MediaController::class)->group(function () {
    Route::get('/media', 'index')->name('frontend.media');
    Route::get('/media/photo-gallery', 'photos')->name('frontend.photo-gallery');
    Route::get('/media/video-gallery', 'videos')->name('frontend.video-gallery');
    Route::get('/media/latest-notice', 'notices')->name('frontend.latest-notice');
});


// Error route
Route::get('/error', function () {
    abort(500);
});

// Socialite routes
Route::get('/auth/redirect/{provider}', [SocialiteController::class, 'redirect']);

// Auth routes
require __DIR__ . '/auth.php';
