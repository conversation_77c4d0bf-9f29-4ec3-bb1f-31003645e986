<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
    
    /**
     * Display a listing of the resource.
     */
    public function aboutUs()
    {
        return view('pages.frontend.about-us');
    }

    /**
     * Display a listing of the resource.
     */
    public function contactUs()
    {
        return view('pages.frontend.contact-us');
    }

    /**
     * Display a listing of the resource.
     */
    public function faq()
    {
        return view('pages.frontend.faq');
    }

    /**
     * Display a listing of the resource.
     */
    public function rules()
    {
        return view('pages.frontend.rules');
    }
}
