<script>
    class MobileNavigation {
        constructor() {
            this.init();
            this.setupEventListeners();
        }

        init() {
            this.navItems = document.querySelectorAll(".mobile-navigation .list");
            this.overlay = this.createTransitionOverlay();
            this.isNavigating = false;
        }

        createTransitionOverlay() {
            const overlay = document.createElement('div');
            overlay.className = 'page-transition-overlay';
            overlay.innerHTML = '<div class="spinner"></div>';
            document.body.appendChild(overlay);
            return overlay;
        }

        setupEventListeners() {
            this.navItems.forEach((item, index) => {
                const link = item.querySelector('a');

                // Click event with enhanced feedback
                item.addEventListener('click', (e) => this.handleNavClick(e, item, index));

                // Touch events for better mobile experience
                item.addEventListener('touchstart', () => this.addTouchFeedback(item));
                item.addEventListener('touchend', () => this.removeTouchFeedback(item));

                // Hover effects for devices that support it
                if (window.matchMedia('(hover: hover)').matches) {
                    item.addEventListener('mouseenter', () => this.addHoverEffect(item));
                    item.addEventListener('mouseleave', () => this.removeHoverEffect(item));
                }
            });

            // Livewire navigation events
            document.addEventListener('livewire:navigate', () => this.showLoadingState());
            document.addEventListener('livewire:navigated', () => this.hideLoadingState());
            document.addEventListener('livewire:navigating', () => this.showTransition());
        }

        handleNavClick(e, item, index) {
            if (this.isNavigating) {
                e.preventDefault();
                return;
            }

            // Haptic feedback simulation
            this.simulateHapticFeedback();

            // Visual feedback
            this.setActiveItem(item);
            this.showLoadingIndicator(item);

            // Smooth transition
            this.startNavigation();
        }

        setActiveItem(activeItem) {
            this.navItems.forEach(item => {
                item.classList.remove('active');
                this.hideLoadingIndicator(item);
            });
            activeItem.classList.add('active');

            // Force indicator positioning update
            this.updateIndicatorPosition(activeItem);
        }

        updateIndicatorPosition(activeItem) {
            const indicator = document.querySelector('.mobile-navigation .indicator');
            if (indicator && activeItem) {
                // Get the index of the active item
                const index = Array.from(this.navItems).indexOf(activeItem);

                // Calculate position based on index
                const position = `calc(10% + ${index} * 20%)`;

                // Apply the position
                indicator.style.left = position;
                indicator.style.opacity = '1';
                indicator.style.visibility = 'visible';

                // Add a small delay to ensure smooth transition
                setTimeout(() => {
                    indicator.style.transform = 'translateX(-50%) scale(1.1)';
                    setTimeout(() => {
                        indicator.style.transform = 'translateX(-50%) scale(1)';
                    }, 150);
                }, 50);
            }
        }

        showLoadingIndicator(item) {
            item.classList.add('loading');
        }

        hideLoadingIndicator(item) {
            item.classList.remove('loading');
        }

        addTouchFeedback(item) {
            item.style.transform = 'scale(0.95)';
        }

        removeTouchFeedback(item) {
            item.style.transform = '';
        }

        addHoverEffect(item) {
            if (!item.classList.contains('active')) {
                item.style.transform = 'translateY(-2px)';
            }
        }

        removeHoverEffect(item) {
            if (!item.classList.contains('active')) {
                item.style.transform = '';
            }
        }

        simulateHapticFeedback() {
            // Vibration API for supported devices
            if ('vibrate' in navigator) {
                navigator.vibrate(50);
            }
        }

        startNavigation() {
            this.isNavigating = true;
        }

        showLoadingState() {
            document.body.classList.add('navigation-loading');
        }

        hideLoadingState() {
            this.isNavigating = false;
            document.body.classList.remove('navigation-loading');

            // Hide all loading indicators
            this.navItems.forEach(item => this.hideLoadingIndicator(item));

            // Update active state based on current route
            this.updateActiveStateFromRoute();
        }

        showTransition() {
            this.overlay.classList.add('active');
            setTimeout(() => {
                this.overlay.classList.remove('active');
            }, 300);
        }

        updateActiveStateFromRoute() {
            const currentPath = window.location.pathname;
            let activeItemFound = false;

            this.navItems.forEach(item => {
                const link = item.querySelector('a');
                const href = link.getAttribute('href');

                if (href && (currentPath === href || currentPath.startsWith(href + '/'))) {
                    this.setActiveItem(item);
                    activeItemFound = true;
                }
            });

            // If no specific match found, check for default (home)
            if (!activeItemFound && currentPath === '/') {
                const homeItem = this.navItems[0]; // Assuming first item is home
                if (homeItem) {
                    this.setActiveItem(homeItem);
                }
            }
        }
    }

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', () => {
        new MobileNavigation();
    });

    // Re-initialize after Livewire navigation
    document.addEventListener('livewire:navigated', () => {
        // Small delay to ensure DOM is updated
        setTimeout(() => {
            new MobileNavigation();
        }, 100);
    });

    // Performance optimization: Preload pages on hover
    document.addEventListener('DOMContentLoaded', () => {
        const navLinks = document.querySelectorAll('.mobile-navigation .nav-link');

        navLinks.forEach(link => {
            link.addEventListener('mouseenter', () => {
                // Preload the page for faster navigation
                const href = link.getAttribute('href');
                if (href && !link.dataset.preloaded) {
                    const preloadLink = document.createElement('link');
                    preloadLink.rel = 'prefetch';
                    preloadLink.href = href;
                    document.head.appendChild(preloadLink);
                    link.dataset.preloaded = 'true';
                }
            });
        });
    });
</script>
