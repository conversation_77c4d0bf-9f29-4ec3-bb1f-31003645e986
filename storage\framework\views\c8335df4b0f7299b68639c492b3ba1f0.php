
<div class="mobile-nav-container" id="mobileNav">
    <ul class="mobile-nav-list">
        <?php $__currentLoopData = $navigationItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="mobile-nav-item list <?php echo e($isActive($item) ? 'active' : ''); ?>"
                data-route="<?php echo e($item['data_route']); ?>">
                <a href="<?php echo e(route($item['route'])); ?>"
                   wire:navigate
                   class="mobile-nav-link nav-link">
                    <span class="mobile-nav-icon icon">
                        <?php echo getIcon($item['icon'], 'text-[1.2em] transition-all duration-300'); ?>

                    </span>
                    <span class="mobile-nav-text text">
                        <?php echo e($item['text']); ?>

                    </span>
                    <span class="mobile-nav-loader loading-indicator"></span>
                </a>
            </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>
</div>


<?php $__env->startPush('styles'); ?>
<style>
    @media (max-width: 991.98px) {
        body {
            padding-bottom: 85px;
        }
    }
</style>
<?php $__env->stopPush(); ?>


<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/components/mobile-navigation.js')); ?>"></script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\resources\views/components/frontend/navigation/mobile-navigation.blade.php ENDPATH**/ ?>