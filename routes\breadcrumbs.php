<?php

use App\Models\User;
use Diglactic\Breadcrumbs\Breadcrumbs;
use Diglactic\Breadcrumbs\Generator as BreadcrumbTrail;
use Spatie\Permission\Models\Role;

// Dashboard
Breadcrumbs::for('dashboard', function (BreadcrumbTrail $trail) {
    $trail->push('Dashboard', route('dashboard'));
});

// Dashboard > User Management
Breadcrumbs::for('user-management.index', function (BreadcrumbTrail $trail) {
    $trail->parent('dashboard');
    $trail->push('User Management', route('user-management.users.index'));
});

// Dashboard > User Management > Users
Breadcrumbs::for('user-management.users.index', function (BreadcrumbTrail $trail) {
    $trail->parent('user-management.index');
    $trail->push('Users', route('user-management.users.index'));
});

// Dashboard > User Management > Users > [User]
Breadcrumbs::for('user-management.users.show', function (BreadcrumbTrail $trail, User $user) {
    $trail->parent('user-management.users.index');
    $trail->push(ucwords($user->name), route('user-management.users.show', $user));
});

// Dashboard > User Management > Roles
Breadcrumbs::for('user-management.roles.index', function (BreadcrumbTrail $trail) {
    $trail->parent('user-management.index');
    $trail->push('Roles', route('user-management.roles.index'));
});

// Dashboard > User Management > Roles > [Role]
Breadcrumbs::for('user-management.roles.show', function (BreadcrumbTrail $trail, Role $role) {
    $trail->parent('user-management.roles.index');
    $trail->push(ucwords($role->name), route('user-management.roles.show', $role));
});

// Dashboard > User Management > Permission
Breadcrumbs::for('user-management.permissions.index', function (BreadcrumbTrail $trail) {
    $trail->parent('user-management.index');
    $trail->push('Permissions', route('user-management.permissions.index'));
});

// Dashboard > Account
Breadcrumbs::for('user-profile.overview.index', function (BreadcrumbTrail $trail) {
    $trail->parent('dashboard');
    $trail->push('Profile Overview', route('user-profile.overview.index'));
});




//Frontend Breadcrumbs

// Home
Breadcrumbs::for('home', function (BreadcrumbTrail $trail) {
    $trail->push('Home', route('frontend.home'));
});

// Home > About Us
Breadcrumbs::for('aboutUs', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('About Us', route('frontend.about-us'));
});

// Home > Contact Us
Breadcrumbs::for('contactUs', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Contact Us', route('frontend.contact-us'));
});

// Home > FAQ
Breadcrumbs::for('faq', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('FAQ', route('frontend.faq'));
});

// Home > Rules
Breadcrumbs::for('rules', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Rules', route('frontend.rules'));
});

// Home > Committee
Breadcrumbs::for('committee', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Committee', route('frontend.committee'));
});

// Home > Members
Breadcrumbs::for('members', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Members', route('frontend.members'));
});

// Home > Projects
Breadcrumbs::for('projects', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Projects', route('frontend.projects'));
});

// Home > Projects > Library
Breadcrumbs::for('library', function (BreadcrumbTrail $trail) {
    $trail->parent('projects');
    $trail->push('Library', route('frontend.library-project'));
});

// Home > Projects > Tree Plantation
Breadcrumbs::for('treePlantation', function (BreadcrumbTrail $trail) {
    $trail->parent('projects');
    $trail->push('Tree Plantation', route('frontend.tree-plantation-project'));
});

// Home > Projects > Education
Breadcrumbs::for('education', function (BreadcrumbTrail $trail) {
    $trail->parent('projects');
    $trail->push('Education', route('frontend.education-project'));
});

// Home > Projects > Food
Breadcrumbs::for('food', function (BreadcrumbTrail $trail) {
    $trail->parent('projects');
    $trail->push('Food', route('frontend.food-project'));
});

// Home > Projects > Cloth
Breadcrumbs::for('cloth', function (BreadcrumbTrail $trail) {
    $trail->parent('projects');
    $trail->push('Cloth', route('frontend.cloth-project'));
});

// Home > Projects > Talent Student Award
Breadcrumbs::for('talentStudentAward', function (BreadcrumbTrail $trail) {
    $trail->parent('projects');
    $trail->push('Talent Student Award', route('frontend.talent-student-award'));
});

// Home> Scholarship
Breadcrumbs::for('scholarship', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Scholarship', route('frontend.scholarship'));
});

// Home > Scholarship > Application
Breadcrumbs::for('application', function (BreadcrumbTrail $trail) {
    $trail->parent('scholarship');
    $trail->push('Application', route('frontend.scholarship-application'));
});

// Home > Scholarship > Meritorious Student
Breadcrumbs::for('meritoriousStudent', function (BreadcrumbTrail $trail) {
    $trail->parent('scholarship');
    $trail->push('Meritorious Student', route('frontend.meritorious-student'));
});

// Home > Blood Donor
Breadcrumbs::for('bloodDonor', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Blood Donor', route('frontend.blood-donor'));
});

// Home > Blood Donor > Join
Breadcrumbs::for('bloodDonorJoin', function (BreadcrumbTrail $trail) {
    $trail->parent('bloodDonor');
    $trail->push('Join', route('frontend.blood-donor-join'));
});

// Home > Donation
Breadcrumbs::for('donation', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Donation', route('frontend.donation'));
});

// Home > Donation > About Donation
Breadcrumbs::for('aboutDonation', function (BreadcrumbTrail $trail) {
    $trail->parent('donation');
    $trail->push('About Donation', route('frontend.about-donation'));
});

// Home > Donation > Sponsor
Breadcrumbs::for('sponsor', function (BreadcrumbTrail $trail) {
    $trail->parent('donation');
    $trail->push('Sponsor', route('frontend.sponsor-info'));
});

// Home > Media
Breadcrumbs::for('media', function (BreadcrumbTrail $trail) {
    $trail->parent('home');
    $trail->push('Media', route('frontend.media'));
});

// Home > Media > Photos
Breadcrumbs::for('photos', function (BreadcrumbTrail $trail) {
    $trail->parent('media');
    $trail->push('Photos', route('frontend.photo-gallery'));
});

// Home > Media > Videos
Breadcrumbs::for('videos', function (BreadcrumbTrail $trail) {
    $trail->parent('media');
    $trail->push('Videos', route('frontend.video-gallery'));
});

// Home > Media > Notices
Breadcrumbs::for('notices', function (BreadcrumbTrail $trail) {
    $trail->parent('media');
    $trail->push('Notices', route('frontend.latest-notice'));
});
