<!--begin::Menu wrapper-->
<div class="app-header-menu app-header-mobile-drawer align-items-stretch" data-kt-drawer="true" data-kt-drawer-name="app-header-menu" data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="250px" data-kt-drawer-direction="end" data-kt-drawer-toggle="#kt_app_header_menu_toggle" data-kt-swapper="true" data-kt-swapper-mode="{default: 'append', lg: 'prepend'}" data-kt-swapper-parent="{default: '#kt_app_body', lg: '#kt_app_header_wrapper'}" style="z-index: 99 !important;">
	<!--begin::Menu-->
	<div class="menu menu-rounded menu-column menu-lg-row my-5 my-lg-0 align-items-stretch fw-semibold px-2 px-lg-0" id="kt_app_header_menu" data-kt-menu="true">
		<!--begin:Menu item-->
		<div class="menu-item menu-lg-down-accordion me-0 me-lg-2 <?php echo e(request()->routeIs('frontend.home') ? 'here show menu-here-bg' : ''); ?>">
			<!--begin:Menu link-->
			<a href="<?php echo e(route('frontend.home')); ?>" class="menu-link" wire:navigate>
				<span class="d-flex flex-column">
					<span class="menu-title fs-6 fw-bold">Home</span>
				</span>
			</a>
			<!--end:Menu link-->
		</div>
		<!--end:Menu item-->
		<!--begin:Menu item-->
		<div data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-placement="bottom-start" class="menu-item menu-lg-down-accordion me-0 me-lg-2 <?php echo e(in_array(request()->route()->getName(), ['frontend.about-us', 'frontend.faq', 'frontend.rules', 'frontend.committee', 'frontend.members', 'frontend.contact-us']) ? 'here show menu-here-bg' : ''); ?>">
			<!--begin:Menu link-->
			<span class="menu-link">
				<span class="menu-title">About Us</span>
				<span class="menu-arrow d-lg-none"></span>
			</span>
			<!--end:Menu link-->
			<!--begin:Menu sub-->
			<div class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown p-0 w-100 w-lg-850px">
				<?php echo $__env->make(config('settings.KT_THEME_LAYOUT_DIR').'/partials/frontend/header-layout/header/_menu/__about-us', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
			</div>
			<!--end:Menu sub-->
		</div>
		<!--end:Menu item-->
		<!--begin:Menu item-->
		<div data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-placement="bottom-start" class="menu-item menu-lg-down-accordion me-0 me-lg-2 <?php echo e(in_array(request()->route()->getName(), ['frontend.projects', 'frontend.library-project', 'frontend.tree-plantation-project', 'frontend.education-project', 'frontend.food-project', 'frontend.cloth-project', 'frontend.talent-student-award']) ? 'here show menu-here-bg' : ''); ?>">
			<!--begin:Menu link-->
			<span class="menu-link">
				<span class="menu-title">Projects</span>
				<span class="menu-arrow d-lg-none"></span>
			</span>
			<!--end:Menu link-->
			<!--begin:Menu sub-->
			<div class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown p-0 w-100 w-lg-850px">
				<?php echo $__env->make(config('settings.KT_THEME_LAYOUT_DIR').'/partials/frontend/header-layout/header/_menu/__projects', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
			</div>
			<!--end:Menu sub-->
		</div>
		<!--end:Menu item-->
		<!--begin:Menu item-->
		<div data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-placement="bottom-start" class="menu-item menu-lg-down-accordion me-0 me-lg-2 <?php echo e(in_array(request()->route()->getName(), ['frontend.scholarship', 'frontend.scholarship-application', 'frontend.meritorious-student']) ? 'here show menu-here-bg' : ''); ?>">
			<!--begin:Menu link-->
			<span class="menu-link">
				<span class="menu-title">Scholarship</span>
				<span class="menu-arrow d-lg-none"></span>
			</span>
			<!--end:Menu link-->
			<!--begin:Menu sub-->
			<div class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown p-0 w-100 w-lg-850px">
				<?php echo $__env->make(config('settings.KT_THEME_LAYOUT_DIR').'/partials/frontend/header-layout/header/_menu/__scholarship', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
			</div>
			<!--end:Menu sub-->
		</div>
		<!--end:Menu item-->
		<!--begin:Menu item-->
		<div data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-placement="bottom-start" class="menu-item menu-lg-down-accordion me-0 me-lg-2 <?php echo e(in_array(request()->route()->getName(), ['frontend.blood-donor', 'frontend.blood-donor-join']) ? 'here show menu-here-bg' : ''); ?>">
			<!--begin:Menu link-->
			<span class="menu-link">
				<span class="menu-title">Blood Donor</span>
				<span class="menu-arrow d-lg-none"></span>
			</span>
			<!--end:Menu link-->
			<!--begin:Menu sub-->
			<div class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown p-0 w-100 w-lg-850px">
				<?php echo $__env->make(config('settings.KT_THEME_LAYOUT_DIR').'/partials/frontend/header-layout/header/_menu/__blood-donor', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
			</div>
			<!--end:Menu sub-->
		</div>
		<!--end:Menu item-->
		<!--begin:Menu item-->
		<div data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-placement="bottom-start" class="menu-item menu-lg-down-accordion me-0 me-lg-2 <?php echo e(in_array(request()->route()->getName(), ['frontend.donation', 'frontend.about-donation', 'frontend.sponsor-info']) ? 'here show menu-here-bg' : ''); ?>">
			<!--begin:Menu link-->
			<span class="menu-link">
				<span class="menu-title">Donation</span>
				<span class="menu-arrow d-lg-none"></span>
			</span>
			<!--end:Menu link-->
			<!--begin:Menu sub-->
			<div class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown p-0 w-100 w-lg-850px">
				<?php echo $__env->make(config('settings.KT_THEME_LAYOUT_DIR').'/partials/frontend/header-layout/header/_menu/__donation', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
			</div>
			<!--end:Menu sub-->
		</div>
		<!--end:Menu item-->
		<!--begin:Menu item-->
		<div data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-placement="bottom-start" class="menu-item menu-lg-down-accordion me-0 me-lg-2 <?php echo e(in_array(request()->route()->getName(), ['frontend.media', 'frontend.photo-gallery', 'frontend.video-gallery', 'frontend.latest-notice']) ? 'here show menu-here-bg' : ''); ?>">
			<!--begin:Menu link-->
			<span class="menu-link">
				<span class="menu-title">Media</span>
				<span class="menu-arrow d-lg-none"></span>
			</span>
			<!--end:Menu link-->
			<!--begin:Menu sub-->
			<div class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown p-0 w-100 w-lg-850px">
				<?php echo $__env->make(config('settings.KT_THEME_LAYOUT_DIR').'/partials/frontend/header-layout/header/_menu/__media', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
			</div>
			<!--end:Menu sub-->
		</div>
		<!--end:Menu item-->
		<!--begin:Menu item-->
		<div data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-placement="bottom-start" class="menu-item menu-lg-down-accordion menu-sub-lg-down-indention me-0 me-lg-2">
			<!--begin:Menu link-->
			<span class="menu-link">
				<span class="menu-title">Apps</span>
				<span class="menu-arrow d-lg-none"></span>
			</span>
			<!--end:Menu link-->
			<!--begin:Menu sub-->
			<div class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown px-lg-2 py-lg-4 w-lg-250px">
				<?php echo $__env->make(config('settings.KT_THEME_LAYOUT_DIR').'/partials/frontend/header-layout/header/_menu/__apps', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
			</div>
			<!--end:Menu sub-->
		</div>
		<!--end:Menu item-->
		<!--begin:Menu item-->
		<div data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-placement="bottom-start" class="menu-item menu-lg-down-accordion menu-sub-lg-down-indention me-0 me-lg-2">
			<!--begin:Menu link-->
			<span class="menu-link">
				<span class="menu-title">Help</span>
				<span class="menu-arrow d-lg-none"></span>
			</span>
			<!--end:Menu link-->
			<!--begin:Menu sub-->
			<div class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown px-lg-2 py-lg-4 w-lg-200px">
				<!--begin:Menu item-->
				<div class="menu-item">
					<!--begin:Menu link-->
					<a class="menu-link" href="https://preview.keenthemes.com/html/metronic/docs/base/utilities" target="_blank" title="Check out over 200 in-house components, plugins and ready for use solutions" data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-dismiss="click" data-bs-placement="right">
						<span class="menu-icon"><?php echo getIcon('rocket', 'fs-2'); ?></span>
						<span class="menu-title">Components</span>
					</a>
					<!--end:Menu link-->
				</div>
				<!--end:Menu item-->
				<!--begin:Menu item-->
				<div class="menu-item">
					<!--begin:Menu link-->
					<a class="menu-link" href="https://preview.keenthemes.com/html/metronic/docs" target="_blank" title="Check out the complete documentation" data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-dismiss="click" data-bs-placement="right">
						<span class="menu-icon"><?php echo getIcon('abstract-26', 'fs-2'); ?></span>
						<span class="menu-title">Documentation</span>
					</a>
					<!--end:Menu link-->
				</div>
				<!--end:Menu item-->
				<!--begin:Menu item-->
				<div class="menu-item">
					<!--begin:Menu link-->
					<a class="menu-link" href="https://preview.keenthemes.com/laravel/metronic/docs/changelog" target="_blank">
						<span class="menu-icon"><?php echo getIcon('code', 'fs-2'); ?></span>
						<span class="menu-title">Changelog v8.2.8</span>
					</a>
					<!--end:Menu link-->
				</div>
				<!--end:Menu item-->
			</div>
			<!--end:Menu sub-->
		</div>
		<!--end:Menu item-->
	</div>
	<!--end::Menu-->
</div>
<!--end::Menu wrapper--><?php /**PATH C:\xampp\htdocs\resources\views/layout/partials/frontend/header-layout/header/_menu/_menu.blade.php ENDPATH**/ ?>