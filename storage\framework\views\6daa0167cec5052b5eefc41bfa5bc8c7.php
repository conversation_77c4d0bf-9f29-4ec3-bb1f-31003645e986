<style>
    .mobile-navigation {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 75px;
        background: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        z-index: 1000;
    }



    .mobile-navigation ul {
        display: flex;
        width: 100%;
        max-width: 400px;
        justify-content: space-around;
        position: relative;
        z-index: 2;
    }

    .mobile-navigation ul li {
        position: relative;
        list-style: none;
        width: 70px;
        height: 70px;
        z-index: 3;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .mobile-navigation ul li a {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        width: 100%;
        text-align: center;
        font-weight: 500;
        text-decoration: none;
        border-radius: 15px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
    }

    .mobile-navigation ul li a:hover {
        transform: translateY(-2px);
        background: rgba(255, 255, 255, 0.1);
    }

    .mobile-navigation ul li a .icon {
        position: relative;
        display: block;
        line-height: 40px;
        font-size: 1.4em;
        text-align: center;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        color: #6c757d;
        z-index: 2;
        margin-top: 8px;
    }

    .mobile-navigation ul li a .icon i {
        font-size: 1.2em;
        transition: all 0.3s ease;
    }

    @keyframes iconPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    .mobile-navigation ul li a .text {
        position: relative;
        color: #6c757d;
        font-weight: 500;
        font-size: 0.7em;
        letter-spacing: 0.05em;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        opacity: 1;
        margin-top: 4px;
        margin-bottom: 8px;
        z-index: 2;
    }

    @keyframes textGlow {
        0% { text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); }
        100% { text-shadow: 0 0 10px rgba(255, 255, 255, 0.6); }
    }

    /* Loading Indicator */
    .mobile-navigation ul li a .loading-indicator {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top: 2px solid #ffffff;
        border-radius: 50%;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 4;
    }

    .mobile-navigation ul li.loading a .loading-indicator {
        opacity: 1;
        visibility: visible;
        animation: spin 1s linear infinite;
    }

    .mobile-navigation ul li.loading a .icon,
    .mobile-navigation ul li.loading a .text {
        opacity: 0.3;
    }

    @keyframes spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    /* Active Item Background Highlight */
    .mobile-navigation ul li.active a {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.25), rgba(118, 75, 162, 0.25));
        border-radius: 12px;
        margin: 8px 4px;
        padding: 4px;
    }

    .mobile-navigation ul li.active a .icon {
        color: #667eea;
        text-shadow: 0 0 10px rgba(102, 126, 234, 0.6);
        animation: activeGlow 2s ease-in-out infinite alternate;
    }

    .mobile-navigation ul li.active a .text {
        color: #667eea;
        font-weight: 600;
        text-shadow: 0 0 8px rgba(102, 126, 234, 0.4);
    }

    @keyframes activeGlow {
        0% {
            text-shadow: 0 0 10px rgba(102, 126, 234, 0.6);
        }
        100% {
            text-shadow: 0 0 15px rgba(102, 126, 234, 0.8), 0 0 20px rgba(102, 126, 234, 0.4);
        }
    }



    /* Ripple Effect */
    .mobile-navigation ul li a::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        transform: translate(-50%, -50%);
        transition: all 0.6s ease;
        z-index: 1;
    }

    .mobile-navigation ul li a:active::before {
        width: 60px;
        height: 60px;
    }

    /* Haptic Feedback Simulation */
    .mobile-navigation ul li a:active {
        transform: scale(0.95);
    }

    /* Page Transition Overlay */
    .page-transition-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .page-transition-overlay.active {
        opacity: 0.9;
        visibility: visible;
    }

    .page-transition-overlay .spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid #ffffff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @media (min-width: 992px) {
        .mobile-navigation {
            display: none;
        }
    }

    @media (max-width: 991.98px) {
        body {
            padding-bottom: 85px;
        }
    }

    @media (max-width: 480px) {
        .mobile-navigation ul {
            max-width: 350px;
        }
    }
</style><?php /**PATH C:\xampp\htdocs\resources\views/layout/partials/frontend/navigation/style/__stype.blade.php ENDPATH**/ ?>