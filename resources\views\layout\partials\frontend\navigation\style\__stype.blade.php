<style>
    .mobile-navigation {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 75px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 20px 20px 0 0;
        box-shadow: 0 -8px 25px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        backdrop-filter: blur(10px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .mobile-navigation::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px 20px 0 0;
        backdrop-filter: blur(10px);
    }

    .mobile-navigation ul {
        display: flex;
        width: 100%;
        max-width: 400px;
        justify-content: space-around;
        position: relative;
        z-index: 2;
    }

    .mobile-navigation ul li {
        position: relative;
        list-style: none;
        width: 70px;
        height: 70px;
        z-index: 3;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .mobile-navigation ul li a {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        width: 100%;
        text-align: center;
        font-weight: 500;
        text-decoration: none;
        border-radius: 15px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
    }

    .mobile-navigation ul li a:hover {
        transform: translateY(-2px);
        background: rgba(255, 255, 255, 0.1);
    }

    .mobile-navigation ul li a .icon {
        position: relative;
        display: block;
        line-height: 75px;
        font-size: 1.6em;
        text-align: center;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        color: rgba(255, 255, 255, 0.7);
        z-index: 2;
    }

    .mobile-navigation ul li.active a .icon {
        transform: translateY(-35px) scale(1.1);
        color: #ffffff;
        text-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
    }

    .mobile-navigation ul li.active a .icon i {
        color: #ffffff;
        font-size: 1.3em;
        animation: iconPulse 2s infinite;
    }

    .mobile-navigation ul li a .icon i {
        font-size: 1.3em;
        transition: all 0.3s ease;
    }

    @keyframes iconPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    .mobile-navigation ul li a .text {
        position: absolute;
        color: rgba(255, 255, 255, 0.9);
        font-weight: 600;
        font-size: 0.75em;
        letter-spacing: 0.05em;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        opacity: 0;
        transform: translateY(25px);
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        z-index: 2;
    }

    .mobile-navigation ul li.active a .text {
        opacity: 1;
        transform: translateY(12px);
        animation: textGlow 2s infinite alternate;
    }

    @keyframes textGlow {
        0% { text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); }
        100% { text-shadow: 0 0 10px rgba(255, 255, 255, 0.6); }
    }

    /* Loading Indicator */
    .mobile-navigation ul li a .loading-indicator {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top: 2px solid #ffffff;
        border-radius: 50%;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 4;
    }

    .mobile-navigation ul li.loading a .loading-indicator {
        opacity: 1;
        visibility: visible;
        animation: spin 1s linear infinite;
    }

    .mobile-navigation ul li.loading a .icon,
    .mobile-navigation ul li.loading a .text {
        opacity: 0.3;
    }

    @keyframes spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    .mobile-navigation .indicator {
        position: absolute;
        top: -55%;
        left: 0;
        width: 65px;
        height: 65px;
        background: linear-gradient(135deg, #ff6b6b, #feca57);
        border-radius: 50%;
        border: 4px solid rgba(255, 255, 255, 0.8);
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        opacity: 0;
        visibility: hidden;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        z-index: 1;
        transform: translateX(-50%);
    }

    .mobile-navigation .indicator::before {
        content: "";
        position: absolute;
        top: 50%;
        left: -22px;
        width: 20px;
        height: 20px;
        background: transparent;
        border-top-right-radius: 20px;
        box-shadow: 1px -10px 0 0 rgba(255, 255, 255, 0.1);
        opacity: 1;
        visibility: visible;
    }

    .mobile-navigation .indicator::after {
        content: "";
        position: absolute;
        top: 50%;
        right: -22px;
        width: 20px;
        height: 20px;
        background: transparent;
        border-top-left-radius: 20px;
        box-shadow: -1px -10px 0 0 rgba(255, 255, 255, 0.1);
        opacity: 1;
        visibility: visible;
    }

    .mobile-navigation ul li.active ~ .indicator {
        opacity: 1;
        visibility: visible;
        animation: indicatorPulse 2s infinite;
    }

    @keyframes indicatorPulse {
        0%, 100% {
            transform: scale(1);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        50% {
            transform: scale(1.05);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
        }
    }

    /* Dynamic Indicator Positioning */
    .mobile-navigation ul li:nth-child(1).active ~ .indicator {
        left: calc(10% + 0 * 20%);
        opacity: 1;
        visibility: visible;
    }
    .mobile-navigation ul li:nth-child(2).active ~ .indicator {
        left: calc(10% + 1 * 20%);
        opacity: 1;
        visibility: visible;
    }
    .mobile-navigation ul li:nth-child(3).active ~ .indicator {
        left: calc(10% + 2 * 20%);
        opacity: 1;
        visibility: visible;
    }
    .mobile-navigation ul li:nth-child(4).active ~ .indicator {
        left: calc(10% + 3 * 20%);
        opacity: 1;
        visibility: visible;
    }
    .mobile-navigation ul li:nth-child(5).active ~ .indicator {
        left: calc(10% + 4 * 20%);
        opacity: 1;
        visibility: visible;
    }

    /* Ripple Effect */
    .mobile-navigation ul li a::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        transform: translate(-50%, -50%);
        transition: all 0.6s ease;
        z-index: 1;
    }

    .mobile-navigation ul li a:active::before {
        width: 60px;
        height: 60px;
    }

    /* Haptic Feedback Simulation */
    .mobile-navigation ul li a:active {
        transform: scale(0.95);
    }

    /* Page Transition Overlay */
    .page-transition-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .page-transition-overlay.active {
        opacity: 0.9;
        visibility: visible;
    }

    .page-transition-overlay .spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid #ffffff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @media (min-width: 992px) {
        .mobile-navigation {
            display: none;
        }
    }

    @media (max-width: 991.98px) {
        body {
            padding-bottom: 85px;
        }

        .mobile-navigation ul li:nth-child(1).active ~ .indicator {
            left: calc(10% + 0 * 20%);
        }
        .mobile-navigation ul li:nth-child(2).active ~ .indicator {
            left: calc(10% + 1 * 20%);
        }
        .mobile-navigation ul li:nth-child(3).active ~ .indicator {
            left: calc(10% + 2 * 20%);
        }
        .mobile-navigation ul li:nth-child(4).active ~ .indicator {
            left: calc(10% + 3 * 20%);
        }
        .mobile-navigation ul li:nth-child(5).active ~ .indicator {
            left: calc(10% + 4 * 20%);
        }
    }

    @media (max-width: 480px) {
        .mobile-navigation ul {
            max-width: 350px;
        }

        .mobile-navigation ul li:nth-child(1).active ~ .indicator {
            left: calc(10% + 0 * 20%);
        }
        .mobile-navigation ul li:nth-child(2).active ~ .indicator {
            left: calc(10% + 1 * 20%);
        }
        .mobile-navigation ul li:nth-child(3).active ~ .indicator {
            left: calc(10% + 2 * 20%);
        }
        .mobile-navigation ul li:nth-child(4).active ~ .indicator {
            left: calc(10% + 3 * 20%);
        }
        .mobile-navigation ul li:nth-child(5).active ~ .indicator {
            left: calc(10% + 4 * 20%);
        }
    }
</style>