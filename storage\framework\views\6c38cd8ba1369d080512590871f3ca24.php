  <!-- Mobile Navigation -->
  <div class="mobile-navigation d-lg-none" id="mobileNav">
    <ul>
      <li class="list <?php echo e(request()->routeIs('frontend.home') ? 'active' : ''); ?>" data-route="frontend.home">
        <a href="<?php echo e(route('frontend.home')); ?>" wire:navigate class="nav-link">
          <span class="icon">
            <?php echo getIcon('home-3', 'text-primary fs-1'); ?>

          </span>
          <span class="text">Home</span>
          <span class="loading-indicator"></span>
        </a>
      </li>
      <li class="list <?php echo e(in_array(request()->route()->getName(), ['frontend.about-us']) ? 'active' : ''); ?>" data-route="frontend.about-us">
        <a href="<?php echo e(route('frontend.about-us')); ?>" wire:navigate class="nav-link">
          <span class="icon">
            <?php echo getIcon('information', 'text-danger fs-1'); ?>

          </span>
          <span class="text">About</span>
          <span class="loading-indicator"></span>
        </a>
      </li>
      <li class="list <?php echo e(in_array(request()->route()->getName(), ['frontend.projects']) ? 'active' : ''); ?>" data-route="frontend.projects">
        <a href="<?php echo e(route('frontend.projects')); ?>" wire:navigate class="nav-link">
          <span class="icon">
            <?php echo getIcon('category', 'text-info fs-1'); ?>

          </span>
          <span class="text">Projects</span>
          <span class="loading-indicator"></span>
        </a>
      </li>
      <li class="list <?php echo e(in_array(request()->route()->getName(), ['frontend.faq']) ? 'active' : ''); ?>" data-route="frontend.faq">
        <a href="<?php echo e(route('frontend.faq')); ?>" wire:navigate class="nav-link">
          <span class="icon">
            <?php echo getIcon('question', 'text-success fs-1'); ?>

          </span>
          <span class="text">FAQs</span>
          <span class="loading-indicator"></span>
        </a>
      </li>
      <li class="list <?php echo e(in_array(request()->route()->getName(), ['frontend.members']) ? 'active' : ''); ?>" data-route="frontend.members">
        <a href="<?php echo e(route('frontend.members')); ?>" wire:navigate class="nav-link">
          <span class="icon">
            <?php echo getIcon('people', 'text-warning fs-1'); ?>

          </span>
          <span class="text">Members</span>
          <span class="loading-indicator"></span>
        </a>
      </li>
    </ul>
  </div>

  <!-- Mobile Navigation CSS -->
  <?php echo $__env->make('layout/partials/frontend/navigation/style/__stype', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

  <!-- Mobile Navigation JS -->
  <?php echo $__env->make('layout/partials/frontend/navigation/style/__script', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php /**PATH C:\xampp\htdocs\resources\views/layout/partials/frontend/navigation/_mobile-navigation.blade.php ENDPATH**/ ?>