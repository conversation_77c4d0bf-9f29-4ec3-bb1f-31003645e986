  <!-- Mobile Navigation -->
  <div class="mobile-navigation d-lg-none" id="mobileNav">
    <ul>
      <li class="list {{ request()->routeIs('frontend.home') ? 'active' : '' }}" data-route="frontend.home">
        <a href="{{ route('frontend.home') }}" wire:navigate class="nav-link">
          <span class="icon">
            {!! getIcon('home-3', 'text-primary fs-1') !!}
          </span>
          <span class="text">Home</span>
          <span class="loading-indicator"></span>
        </a>
      </li>
      <li class="list {{ in_array(request()->route()->getName(), ['frontend.about-us']) ? 'active' : '' }}" data-route="frontend.about-us">
        <a href="{{ route('frontend.about-us') }}" wire:navigate class="nav-link">
          <span class="icon">
            {!! getIcon('information', 'text-danger fs-1') !!}
          </span>
          <span class="text">About</span>
          <span class="loading-indicator"></span>
        </a>
      </li>
      <li class="list {{ in_array(request()->route()->getName(), ['frontend.projects']) ? 'active' : '' }}" data-route="frontend.projects">
        <a href="{{ route('frontend.projects') }}" wire:navigate class="nav-link">
          <span class="icon">
            {!! getIcon('category', 'text-info fs-1') !!}
          </span>
          <span class="text">Projects</span>
          <span class="loading-indicator"></span>
        </a>
      </li>
      <li class="list {{ in_array(request()->route()->getName(), ['frontend.faq']) ? 'active' : '' }}" data-route="frontend.faq">
        <a href="{{ route('frontend.faq') }}" wire:navigate class="nav-link">
          <span class="icon">
            {!! getIcon('question', 'text-success fs-1') !!}
          </span>
          <span class="text">FAQs</span>
          <span class="loading-indicator"></span>
        </a>
      </li>
      <li class="list {{ in_array(request()->route()->getName(), ['frontend.members']) ? 'active' : '' }}" data-route="frontend.members">
        <a href="{{ route('frontend.members') }}" wire:navigate class="nav-link">
          <span class="icon">
            {!! getIcon('people', 'text-warning fs-1') !!}
          </span>
          <span class="text">Members</span>
          <span class="loading-indicator"></span>
        </a>
      </li>
    </ul>
  </div>

  <!-- Mobile Navigation CSS -->
  @include(config('settings.KT_THEME_LAYOUT_DIR').'/partials/frontend/navigation/style/__style')

  <!-- Mobile Navigation JS -->
  @include(config('settings.KT_THEME_LAYOUT_DIR').'/partials/frontend/navigation/style/__script')
