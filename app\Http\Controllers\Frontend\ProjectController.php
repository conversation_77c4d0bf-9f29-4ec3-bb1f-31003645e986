<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ProjectController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('pages.frontend.projects.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    /**
     * Display the library project page.
     */
    public function library()
    {
        return view('pages.frontend.projects.library');
    }

    /**
     * Display the tree plantation project page.
     */
    public function treePlantation()
    {
        return view('pages.frontend.projects.tree-plantation');
    }

    /**
     * Display the education project page.
     */
    public function education()
    {
        return view('pages.frontend.projects.education');
    }

    /**
     * Display the food project page.
     */
    public function food()
    {
        return view('pages.frontend.projects.food');
    }

    /**
     * Display the cloth project page.
     */
    public function cloth()
    {
        return view('pages.frontend.projects.cloth');
    }

    /**
     * Display the talent student award project page.
     */
    public function talentStudentAward()
    {
        return view('pages.frontend.projects.talent-student-award');
    }
    
}
